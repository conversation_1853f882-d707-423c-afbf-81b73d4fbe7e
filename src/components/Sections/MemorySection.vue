<template>
  <div class="memory-section" :class="{ expanded: isMemoryExpanded }">
    <div class="section-header">
      <span class="section-icon">📝</span>
      <span class="section-title">事件记录</span>
      <div class="section-actions">
        <button class="section-add-btn" title="添加" @click="handleMemoryAdd">
          <span class="add-icon">+</span>
        </button>
        <button class="section-expand-btn" :title="isMemoryExpanded ? '收起' : '展开'" @click="toggleMemoryExpanded">
          <div class="triangle-icon" :class="{ expanded: isMemoryExpanded }"></div>
        </button>
      </div>
    </div>
    <div class="section-content" :class="{ expanded: isMemoryExpanded }">
      <div v-if="loadingMemories" class="loading-text">加载中...</div>
      <div v-else-if="memories.length > 0" class="memory-content">
        <!-- 默认只显示一条记录，展开后显示全部 -->
        <div
          v-for="(memory, index) in isMemoryExpanded ? memories : memories.slice(0, 1)"
          :key="memory.event_id"
          class="memory-item"
          @click="handleEditMemory(memory)"
        >
          <div class="memory-header">
            <div class="memory-date">{{ formatMemoryDate(memory.timestamp) }}</div>
            <button class="delete-memory-btn" title="删除" @click.stop="handleDeleteMemory(memory)">
              <span class="delete-icon">×</span>
            </button>
          </div>
          <div class="memory-description">{{ memory.description_text }}</div>
          <div v-if="memory.location" class="memory-location">
            <span class="location-icon">📍</span>
            {{ memory.location }}
          </div>
        </div>

        <!-- 底部收起按钮 - 当展开且项目数量超过3个时显示 -->
        <div v-if="isMemoryExpanded && memories.length > 3" class="section-footer">
          <button class="section-collapse-btn" title="收起" @click="toggleMemoryExpanded">
            <div class="triangle-icon collapsed"></div>
            <span class="collapse-text">收起</span>
          </button>
        </div>
      </div>
      <div v-else class="memory-content">
        <div class="empty-memory">暂无记忆记录，快去和ta聊聊天吧！</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type { IEvent, IPersonDetail } from '@/apis/memory';
import { getPersonMemories } from '@/apis/memory';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  memoryAdd: [];
  editMemory: [memory: IEvent];
  deleteMemory: [memory: IEvent];
}>();

// 响应式数据
const isMemoryExpanded = ref(false);
const memories = ref<IEvent[]>([]);
const loadingMemories = ref(false);

// 切换记忆时刻展开/收起状态
const toggleMemoryExpanded = () => {
  isMemoryExpanded.value = !isMemoryExpanded.value;
};

// 格式化记忆日期
const formatMemoryDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

// 处理记忆时刻添加按钮点击
const handleMemoryAdd = () => {
  emit('memoryAdd');
};

// 处理编辑记忆
const handleEditMemory = (memory: IEvent) => {
  emit('editMemory', memory);
};

// 处理删除记忆
const handleDeleteMemory = (memory: IEvent) => {
  emit('deleteMemory', memory);
};

// 加载记忆数据
const loadMemories = async () => {
  if (!props.personId || !props.userId) {
    memories.value = [];
    return;
  }

  try {
    loadingMemories.value = true;
    console.log('🔄 [MemorySection] 开始获取记忆数据...');

    const response = await getPersonMemories({
      user_id: props.userId,
      person_id: props.personId,
    });

    console.log('📡 [MemorySection] 记忆数据响应:', response);

    if (response && response.result === 'success' && response.events) {
      memories.value = response.events;
      console.log('✅ [MemorySection] 记忆数据加载成功，共', memories.value.length, '条记忆');
    } else {
      console.warn('⚠️ [MemorySection] 记忆数据格式异常:', response);
      memories.value = [];
    }
  } catch (error) {
    console.error('❌ [MemorySection] 获取记忆数据失败:', error);
    memories.value = [];
  } finally {
    loadingMemories.value = false;
  }
};

// 监听props变化，重新加载数据
watch(
  () => [props.personId, props.userId],
  () => {
    void loadMemories();
  },
  { immediate: true },
);

// 组件挂载时加载数据
onMounted(() => {
  void loadMemories();
});
</script>

<style lang="scss" scoped>
.memory-section {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

// 记忆时刻展开/收起控制
.memory-section .section-content {
  max-height: 260px;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: none;
  }
}

// 记忆时刻禁用滑动
.memory-section .section-content {
  overflow: hidden !important;

  &.expanded {
    overflow: visible !important;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }
}

.section-add-btn,
.section-expand-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 2px solid #00bcd4;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(0, 188, 212, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }
}

.section-add-btn {
  .add-icon {
    font-size: 20px;
    font-weight: bold;
    color: #00bcd4;
  }
}

.section-expand-btn {
  .triangle-icon {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid #00bcd4;
    transition: transform 0.3s ease;

    &.expanded {
      transform: rotate(180deg);
    }
  }
}

.section-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

// 记忆时刻特有样式
.memory-content {
  .memory-item {
    background: rgba(0, 188, 212, 0.05);
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    padding: 20px;
    position: relative;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      border-color: rgba(0, 188, 212, 0.5);
      background: rgba(0, 188, 212, 0.08);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);
    }

    .memory-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-right: 40px;

      .memory-date {
        color: rgba(255, 255, 255, 0.8);
        font-size: 26px;
        font-weight: 500;
      }

      .delete-memory-btn {
        position: absolute;
        top: 4px;
        right: 4px;
        background: none;
        border: none;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.6);
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        font-size: 18px;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: #ff6b6b;
          transform: scale(1.1);
        }

        .delete-icon {
          font-size: 30px;
        }
      }
    }

    .memory-description {
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px;
      font-weight: 600;
      line-height: 1.4;
      margin: 8px 0;
    }

    .memory-location {
      color: rgba(255, 255, 255, 0.7);
      font-size: 24px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 8px;

      .location-icon {
        font-size: 22px;
      }
    }
  }

  .empty-memory {
    color: rgba(255, 255, 255, 0.6);
    font-size: 30px;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
    background: rgba(0, 188, 212, 0.05);
    border: 1px dashed rgba(0, 188, 212, 0.3);
    border-radius: 12px;
    line-height: 1.6;
  }
}

// 底部收起按钮样式
.section-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 188, 212, 0.2);
}

.section-collapse-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 24px;
  border: 2px solid #00bcd4;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #00bcd4;
  font-size: 28px;
  font-weight: 500;

  &:hover {
    background: rgba(0, 188, 212, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }

  .triangle-icon {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid #00bcd4;
    transition: transform 0.3s ease;

    &.collapsed {
      transform: rotate(180deg);
    }
  }

  .collapse-text {
    color: #00bcd4;
  }
}
</style>
