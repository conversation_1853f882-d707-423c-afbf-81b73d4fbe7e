<template>
  <div v-if="show" class="dialog-overlay">
    <div class="dialog-container add-dialog">
      <div class="dialog-header">
        <div class="dialog-title">添加事件</div>
        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>

      <div class="dialog-content add-content">
        <!-- 自然语言添加区域 -->
        <div class="natural-add-section">
          <!-- 输入提示 -->
          <div class="input-hint">
            <div class="hint-title">自然语言添加</div>
            <div class="hint-desc">用自然语言描述您的事件，例如：</div>
            <div class="hint-examples">
              <div class="example-item">• 我周三要和张三一起去吃北京麦当劳</div>
              <div class="example-item">• 昨天和小李在公司开了项目会议</div>
              <div class="example-item">• 下周五和朋友们去看电影</div>
            </div>
          </div>

          <!-- 文字输入框 -->
          <div class="input-group">
            <label class="input-label">事件内容</label>
            <textarea
              v-model="inputText"
              class="input-field natural-input"
              placeholder="请用自然语言描述您的事件..."
              maxlength="200"
              rows="3"
              :disabled="isRecording"
            ></textarea>
            <div class="char-count">{{ inputText.length }}/200</div>
          </div>

          <!-- 语音输入时的文字显示区域 -->
          <div v-if="isRecording" class="voice-text-display">
            <div v-if="!recognizedText" class="voice-placeholder">我在听，请说...</div>
            <div v-else class="voice-message-text">{{ recognizedText }}</div>
          </div>

          <!-- 语音输入区域 -->
          <div class="voice-input-area">
            <!-- 默认状态：左侧语音按钮 + 右侧发送按钮 -->
            <div v-if="!isRecording" class="voice-input-default">
              <button class="voice-mic-btn" @click="startVoiceRecording">
                <div class="voice-button-bg"></div>
                <img src="@/assets/icon/mic.png" alt="语音" class="voice-mic-icon" />
              </button>

              <button
                class="send-btn"
                :class="{ 'not-input': !inputText.trim() || isSubmitting }"
                :disabled="!inputText.trim() || isSubmitting"
                @click="handleSubmit"
              >
                <span v-if="isSubmitting">创建中...</span>
                <span v-else>创建事件</span>
              </button>
            </div>

            <!-- 录音状态：居中的停止录音按钮 -->
            <div v-else class="voice-input-recording">
              <button class="voice-stop-btn" @click="stopVoiceRecording">
                <div class="voice-button-bg recording"></div>
                <img src="@/assets/icon/mic.png" alt="停止录音" class="voice-mic-icon" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { addPersonEventNatural, type IAddEventNaturalRequest } from '@/apis/memory';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';

// Props定义
interface IProps {
  show: boolean;
  userId: string;
  personId?: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  success: [];
}>();

// 响应式数据
const inputText = ref('');
const isSubmitting = ref(false);

// 语音相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const audioBufferIndex = ref(0);
const sessionId = ref('');
const lastBuffer = ref<ArrayBuffer | null>(null);

// 录音器和定时器
let recorder: any = null;
let timerId: number | null = null;

// 重置状态
const resetState = () => {
  inputText.value = '';
  isSubmitting.value = false;
  isRecording.value = false;
  recognizedText.value = '';
  audioBufferIndex.value = 0;
  sessionId.value = '';
  lastBuffer.value = null;

  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }

  releaseMicrophoneResources();
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (recorder && recorder.mediaStream) {
    recorder.mediaStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
  }
};

// 取消录音
const cancelRecording = () => {
  isRecording.value = false;
  recognizedText.value = '';
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  releaseMicrophoneResources();
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    stream.getTracks().forEach(track => track.stop());
  } catch (error) {
    console.error('获取麦克风权限失败:', error);
    micPermission.value = false;
    showToast('请允许麦克风权限以使用语音功能');
  }
};

// 初始化录音器
const initRecorder = () => {
  recorder = new Recorder({
    sampleRate: 16000,
    bitRate: 16,
    numChannels: 1,
  });

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      // 修复：检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== recognizedText.value
      ) {
        recognizedText.value = streamData.data.full_text;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 开始语音录音
const startVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，取消录音
    cancelRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    inputText.value = recognizedText.value;
    recognizedText.value = '';
    showToast('语音识别完成');
  } else {
    showToast('录音解析为空，请重新录制~');
  }
};

// 处理提交
const handleSubmit = async () => {
  if (!inputText.value.trim() || isSubmitting.value) {
    return;
  }

  const content = inputText.value.trim();
  await handleSubmitEvent(content);
};

// 提交事件
const handleSubmitEvent = async (eventText: string) => {
  if (!eventText.trim() || isSubmitting.value) return;

  console.log('🚀 [AddEventDialog] 开始提交自然语言事件:', eventText);

  try {
    isSubmitting.value = true;

    // 构建请求参数
    const requestData: IAddEventNaturalRequest = {
      user_id: props.userId,
      event_text: eventText,
    };

    console.log('📤 [AddEventDialog] 自然语言事件请求参数:', requestData);

    // 调用自然语言添加事件API
    const response = await addPersonEventNatural(requestData);

    console.log('📡 [AddEventDialog] 自然语言事件响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [AddEventDialog] 事件添加成功');
      showSuccessToast('事件添加成功！');
      emit('success');
      handleClose();
    } else {
      console.warn('⚠️ [AddEventDialog] 添加事件失败:', response);
      showFailToast('添加事件失败');
    }
  } catch (error) {
    console.error('❌ [AddEventDialog] 提交事件失败:', error);
    showFailToast('网络错误，添加事件失败');
  } finally {
    isSubmitting.value = false;
  }
};

// 监听show变化，重置状态
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      resetState();
    }
  },
);

// 处理关闭
const handleClose = () => {
  if (isSubmitting.value) return;

  resetState();
  emit('close');
};

// 组件卸载时清理
onBeforeUnmount(() => {
  resetState();
});
</script>

<style scoped lang="scss">
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.add-dialog {
  width: 500px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dialog-title {
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.dialog-close {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
  }
}

.close-icon {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

.dialog-content {
  padding: 28px;
}

.add-content {
  max-height: 70vh;
  overflow-y: auto;
}

.natural-add-section {
  .input-hint {
    margin-bottom: 24px;
    padding: 20px;
    background: rgba(0, 188, 212, 0.05);
    border-radius: 12px;
    border-left: 4px solid #00bcd4;

    .hint-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
    }

    .hint-desc {
      font-size: 16px;
      color: #666;
      margin-bottom: 16px;
    }

    .hint-examples {
      .example-item {
        font-size: 16px;
        color: #888;
        margin-bottom: 8px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .input-group {
    margin-bottom: 24px;

    .input-label {
      display: block;
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .input-field {
      width: 100%;
      padding: 16px;
      border: 2px solid rgba(0, 188, 212, 0.2);
      border-radius: 12px;
      font-size: 16px;
      line-height: 1.5;
      background: rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;
      resize: vertical;
      min-height: 80px;

      &:focus {
        outline: none;
        border-color: #00bcd4;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 4px rgba(0, 188, 212, 0.1);
      }

      &::placeholder {
        color: #999;
      }

      &:disabled {
        background: rgba(0, 0, 0, 0.05);
        color: #999;
        cursor: not-allowed;
      }
    }

    .char-count {
      text-align: right;
      font-size: 14px;
      color: #999;
      margin-top: 8px;
    }
  }

  .voice-text-display {
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(0, 188, 212, 0.05);
    border-radius: 12px;
    border: 2px solid rgba(0, 188, 212, 0.2);

    .voice-placeholder {
      color: #999;
      font-style: italic;
      text-align: center;
    }

    .voice-message-text {
      color: #333;
      line-height: 1.5;
    }
  }

  .voice-input-area {
    .voice-input-default {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .voice-input-recording {
      display: flex;
      justify-content: center;
    }

    .voice-mic-btn,
    .voice-stop-btn {
      position: relative;
      width: 56px;
      height: 56px;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      .voice-button-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 50%;
        background: rgba(240, 240, 240, 0.9);
        transition: all 0.3s ease;

        &.recording {
          background: rgba(255, 82, 82, 0.9);
          animation: pulse 1.5s infinite;
        }
      }

      .voice-mic-icon {
        width: 24px;
        height: 24px;
        z-index: 1;
        filter: brightness(0) invert(1);
      }

      &:hover {
        transform: scale(1.1);

        .voice-button-bg {
          background: rgba(220, 220, 220, 0.9);

          &.recording {
            background: rgba(255, 60, 60, 0.9);
          }
        }
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.1);
        opacity: 0.7;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }

    .send-btn {
      flex: 1;
      height: 56px;
      background: linear-gradient(135deg, #00bcd4, #0097a7);
      color: white;
      border: none;
      border-radius: 28px;
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #0097a7, #00838f);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 188, 212, 0.4);
      }

      &:active:not(:disabled) {
        transform: translateY(0);
      }

      &.not-input,
      &:disabled {
        background: rgba(0, 0, 0, 0.1);
        color: rgba(0, 0, 0, 0.3);
        cursor: not-allowed;
        box-shadow: none;
        transform: none;
      }
    }
  }
}
</style>
